import pytesseract
from PIL import Image
import os

# Configurar o caminho do Tesseract (ajuste conforme sua instalação)
pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

def testar_ocr():
    """Testa o OCR com imagens existentes na pasta telefones"""
    
    if not os.path.exists('telefones'):
        print("Pasta 'telefones' não encontrada. Execute o script principal primeiro.")
        return
    
    # Listar arquivos na pasta telefones
    arquivos = [f for f in os.listdir('telefones') if f.endswith('.png')]
    
    if not arquivos:
        print("Nenhuma imagem encontrada na pasta 'telefones'.")
        return
    
    print(f"Encontradas {len(arquivos)} imagens para testar OCR:")
    print("-" * 50)
    
    for arquivo in sorted(arquivos):
        caminho_completo = os.path.join('telefones', arquivo)
        
        try:
            # Abrir imagem
            img = Image.open(caminho_completo)
            
            # Diferentes configurações de OCR para testar
            configs = [
                '--psm 8 -c tessedit_char_whitelist=0123456789()+- ',  # Apenas números e símbolos de telefone
                '--psm 7',  # Linha única de texto
                '--psm 6',  # Bloco uniforme de texto
                '--psm 13'  # Linha crua - trata a imagem como uma única linha de texto
            ]
            
            print(f"\nArquivo: {arquivo}")
            
            for i, config in enumerate(configs, 1):
                try:
                    texto = pytesseract.image_to_string(img, config=config)
                    texto_limpo = texto.strip().replace('\n', ' ')
                    
                    # Extrair apenas números
                    numeros = ''.join(filter(str.isdigit, texto))
                    
                    print(f"  Config {i}: '{texto_limpo}' -> Números: '{numeros}'")
                    
                except Exception as e:
                    print(f"  Config {i}: Erro - {e}")
            
        except Exception as e:
            print(f"Erro ao processar {arquivo}: {e}")

def testar_ocr_manual():
    """Permite testar OCR em uma imagem específica"""
    nome_arquivo = input("Digite o nome do arquivo (ex: contato-1.png): ")
    caminho = os.path.join('telefones', nome_arquivo)
    
    if not os.path.exists(caminho):
        print(f"Arquivo {caminho} não encontrado.")
        return
    
    try:
        img = Image.open(caminho)
        
        # Mostrar informações da imagem
        print(f"\nImagem: {nome_arquivo}")
        print(f"Tamanho: {img.size}")
        print(f"Modo: {img.mode}")
        
        # Testar diferentes configurações
        configs = {
            'Padrão': '',
            'Números apenas': '--psm 8 -c tessedit_char_whitelist=0123456789()+- ',
            'Linha única': '--psm 7',
            'Bloco texto': '--psm 6',
            'Linha crua': '--psm 13'
        }
        
        print("\nResultados OCR:")
        print("-" * 30)
        
        for nome, config in configs.items():
            try:
                texto = pytesseract.image_to_string(img, config=config)
                texto_limpo = texto.strip().replace('\n', ' ')
                numeros = ''.join(filter(str.isdigit, texto))
                
                print(f"{nome:15}: '{texto_limpo}'")
                print(f"{'Números':15}: '{numeros}'")
                print()
                
            except Exception as e:
                print(f"{nome:15}: Erro - {e}")
                
    except Exception as e:
        print(f"Erro ao processar imagem: {e}")

if __name__ == "__main__":
    print("=== TESTE DE OCR ===")
    print("1. Testar todas as imagens")
    print("2. Testar imagem específica")
    
    opcao = input("\nEscolha uma opção (1 ou 2): ")
    
    if opcao == "1":
        testar_ocr()
    elif opcao == "2":
        testar_ocr_manual()
    else:
        print("Opção inválida.")
