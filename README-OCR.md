# Automação WhatsApp com OCR - Tesseract

Este projeto automatiza a extração de números de telefone usando OCR (Optical Character Recognition) com Tesseract.

## 📋 Pré-requisitos

### 1. Instalar Tesseract OCR

1. **Baixe o instalador**: [tesseract-ocr-w64-setup-5.4.0.20240606.exe](https://digi.bib.uni-mannheim.de/tesseract/tesseract-ocr-w64-setup-5.4.0.20240606.exe)

2. **Execute o instalador** como administrador

3. **Durante a instalação**:
   - Mantenha o caminho padrão: `C:\Program Files\Tesseract-OCR\`
   - Certifique-se de instalar os dados de idioma (português e inglês)

4. **Verifique a instalação**:
   ```bash
   tesseract --version
   ```

### 2. Instalar Dependências Python

```bash
pip install pytesseract pillow pyautogui pyperclip
```

## 📁 Estrutura dos Arquivos

- `script-whatsapp-ocr.py` - Script principal de automação
- `teste-ocr.py` - Script para testar OCR em imagens existentes
- `baixa_auto.py` - Script original de automação
- `telefones/` - Pasta onde são salvas as capturas de tela
- `telefones_extraidos.txt` - Arquivo com resultados finais

## 🚀 Como Usar

### 1. Script Principal (script-whatsapp-ocr.py)

Este script automatiza todo o processo:

```python
python script-whatsapp-ocr.py
```

**O que ele faz:**
1. Para cada código da lista:
   - Copia o código para área de transferência
   - Clica no campo (coordenada 1046, 129)
   - Cola o código (Ctrl+V)
   - Aguarda 3.2 segundos
   - Captura screenshot da área (778,260 até 919,281)
   - Salva como `contato-X.png`
   - Extrai número usando OCR
   - Armazena resultado

2. Salva todos os resultados em `telefones_extraidos.txt`

### 2. Teste de OCR (teste-ocr.py)

Para testar o OCR sem executar toda a automação:

```python
python teste-ocr.py
```

**Opções:**
- **Opção 1**: Testa OCR em todas as imagens da pasta `telefones/`
- **Opção 2**: Testa OCR em uma imagem específica

## ⚙️ Configurações

### Ajustar Caminho do Tesseract

Se instalou em local diferente, edite nos scripts:

```python
pytesseract.pytesseract.tesseract_cmd = r'C:\Seu\Caminho\tesseract.exe'
```

### Ajustar Coordenadas

Se as coordenadas não estiverem corretas, modifique:

```python
# Campo para clicar e colar código
clicar(1046, 129)

# Área para capturar telefone (x1, y1, x2, y2)
capturar_telefone(778, 260, 919, 281, nome_arquivo)
```

### Configurações de OCR

O script usa diferentes configurações para melhor reconhecimento:

```python
# Apenas números e símbolos de telefone
config = '--psm 8 -c tessedit_char_whitelist=0123456789()+- '
```

**Outros modos PSM (Page Segmentation Mode):**
- `--psm 6`: Bloco uniforme de texto
- `--psm 7`: Linha única de texto  
- `--psm 8`: Palavra única
- `--psm 13`: Linha crua

## 📊 Resultados

### Arquivo telefones_extraidos.txt
```
CÓDIGOS E TELEFONES EXTRAÍDOS
==================================================

BR2500506652993: 21999887766
BR251996174676D: 11988776655
...
```

### Pasta telefones/
```
telefones/
├── contato-1.png
├── contato-2.png
├── contato-3.png
└── ...
```

## 🔧 Solução de Problemas

### Erro: "tesseract não é reconhecido"
- Verifique se o Tesseract foi instalado corretamente
- Adicione o caminho às variáveis de ambiente do Windows

### OCR não reconhece números
1. Verifique a qualidade da imagem capturada
2. Teste diferentes configurações PSM
3. Ajuste as coordenadas de captura
4. Use o `teste-ocr.py` para experimentar

### Coordenadas incorretas
1. Use `pyautogui.position()` para descobrir coordenadas:
   ```python
   import pyautogui
   print(pyautogui.position())  # Move o mouse e execute
   ```

### Melhorar Precisão do OCR
1. Capture imagens maiores
2. Use pré-processamento de imagem:
   ```python
   from PIL import ImageEnhance
   
   # Aumentar contraste
   enhancer = ImageEnhance.Contrast(img)
   img = enhancer.enhance(2.0)
   ```

## 📝 Exemplo de Uso Completo

```python
# 1. Execute o script principal
python script-whatsapp-ocr.py

# 2. Verifique os resultados
# - Imagens em: ./telefones/
# - Resultados em: telefones_extraidos.txt

# 3. Se precisar testar OCR
python teste-ocr.py
```

## 🎯 Dicas de Otimização

1. **Qualidade da Captura**: Certifique-se que a área capturada contenha apenas o número
2. **Tempo de Espera**: Ajuste os `time.sleep()` conforme necessário
3. **Filtros OCR**: Use whitelist para limitar caracteres reconhecidos
4. **Backup**: Mantenha as imagens para reprocessamento se necessário
