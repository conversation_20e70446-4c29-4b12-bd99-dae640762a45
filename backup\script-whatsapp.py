mensagem = (
    "Boa tarde! aqui é da shoppe!\n"
    "Seu(s) pedidos foram retirados para entrega, eles serão entregues dia 28/07/2025 (segunda-feira).\n"
    "Caso receba uma mensagem de confirmação de entrega, *por favor*, aguarde até as 18h do dia *28/07/2025 (segunda-feira)*, para responder.\n"
    "Caso venha a responder antes, é possível que o entregador tenha que voltar a sua residência solicitando a devolução do pacote. Boa Tarde!\n"
    "Se ficou alguma duvida, responda a essa mensagem."
)


codigos = [
    "BR2500506652993",
    "BR251996174676D",
    "BR250424413953D",
    "BR250331199305Z",
    "BR2553815838771",
    "BR255623955593C",
    "BR2501426109073",
    "BR258377400411W",
    "BR2500351771368",
    "BR2510758510390",
    "BR254652769459E",
    "BR255207455063Y",
    "BR256559684481L",
    "BR259602815928V",
    "BR255969899615T",
    "BR257485283861N",
    "BR251785429955D",
    "BR2578184438424",
    "BR251752404322N",
    "BR251359088276U",
    "BR2547865498446",
    "BR259182188709P",
    "BR254590962637P",
    "BR2566356296773",
    "BR2518394694427",
    "BR2504783060247",
    "BR254393700078K",
    "BR2514808566977",
    "BR255111917692N",
    "BR251140582484P",
    "BR256726339964E",
    "BR250721831144C",
    "BR258076855923R",
    "BR250318397787O",
    "BR2559296332883",
    "BR2556810113138",
    "BR257133667603J",
    "BR250175165026D",
    "BR2599194429724",
    "BR2589084252116",
    "BR2542698288190",
    "BR258270192256C",
    "BR257023498548G",
    "BR258510074697M",
    "BR2583252105514",
    "BR2542499115766",
    "BR250390242846W",
    "BR2547459670434",
    "BR2553979657088",
    "BR255742729790U",
    "BR2550195378254",
    "BR258346126930P",
    "BR254778518462Z",
    "BR2525924596617",
    "BR257833252039Y",
    "BR2554247789987",
    "BR250814539774E",
    "BR256647250302R",
    "BR256027055127S",
    "BR254985020150M",
    "BR253471245646Y",
    "BR253031140828A",
    "BR255790215391J",
    "BR259592349159U",
    "BR2536808063913",
    "BR256461005235I",
    "BR2585987190531",
    "BR2591791752065",
    "BR256772775309C",
    "BR257574364533P",
    "BR252710618928Z",
    "BR257398560059I",
    "BR2581549290645",
    "BR255551283219O",
    "BR2598278147825",
    "BR2505925710395",
    "BR251265553998X",
    "BR2595829467085",
    "BR250614009421B",
    "BR258750043906C",
    "BR253426323571C",
    "BR253924302944M",
    "BR2550790340313",
    "BR257589588972Z",
    "BR2530995635777",
    "BR259782946671V",
    "BR254136289404N",
    "BR2587739188216",
    "BR251255508359T",
    "BR2587354807131",
    "BR259875551466T",
    "BR257322707192J",
    "BR2551734829632",
    "BR258567427781F",
    "BR253409866863S",
    "BR255107270423J"
]


## intruções loop inicial (for i in range(lenght(codigos)))
# clicar(1046,129) # clicar
# time.sleep(1)
# função ctrl+v
# time.sleep(3.2)
# Tira print da area especifica (778,260, 919,281), salva o print em diretorio (telefones) enumerado de acordo com a lista:
# - contato-1.png
# - contato-2.png 
# ....


## Acessar a pasta telefones identificar o numero que está na imagem via detecção OCR usando tesseract
# colocar os numeros em um txt um abaixo do outro *importante: adicionar um + na frente dos numeros*
# enviar a {mensagem} para cada numero via whatsapp





for i in range(len(codigos)):
    ...
