import pyautogui as py
import time
import random

mensagem = (
    "Boa tarde! aqui é da shoppe!\n"
    "Seu(s) pedidos foram retirados para entrega, eles serão entregues dia 28/07/2025 (segunda-feira).\n"
    "Caso receba uma mensagem de confirmação de entrega, *por favor*, aguarde até as 18h do dia *28/07/2025 (segunda-feira)*, para responder.\n"
    "Caso venha a responder antes, é possível que o entregador tenha que voltar a sua residência solicitando a devolução do pacote. Boa Tarde!\n"
    "Se ficou alguma duvida, responda a essa mensagem."
)

def escrever_mensagem(msg):
    py.write(msg)
    time.sleep(2)

def clicar(x, y):
    py.click(x, y)
    time.sleep(2)

def arrastar(x1, y1, x2, y2):
    py.moveTo(x1, y1)
    py.mouseDown()
    time.sleep(1)
    py.moveTo(x2, y2, duration=0.5)
    py.mouseUp()
    time.sleep(2)

def gerar_numero_rg():
    return ''.join([str(random.randint(0, 9)) for _ in range(8)])

def executar_fluxo():
    # 1
    clicar(1052, 637)  # telefone
    time.sleep(1)
    clicar(899, 924)   # whatsapp
    time.sleep(2.5)
    clicar(905, 981)   # caixa de texto
    time.sleep(1)
    escrever_mensagem(mensagem)
    py.press('enter')
    time.sleep(2)

    clicar(825, 1044)  # opções janela
    time.sleep(1)
    clicar(951, 475)   # shoppe
    time.sleep(1)
    clicar(967, 494)   # copiar código
    time.sleep(1)
    clicar(963, 561)   # entrar no pedido
    time.sleep(1)
    clicar(1071, 976)  # entregue
    time.sleep(1)
    clicar(1156, 122)  # editar
    time.sleep(1)
    py.hotkey('ctrl', 'v')
    time.sleep(2)
    clicar(1145, 594)  # confirmar
    time.sleep(1)
    clicar(812, 350)   # outros
    time.sleep(1)
    clicar(1158, 921)  # conexao
    time.sleep(1)
    clicar(969, 975)   # confirmar
    time.sleep(1)
    clicar(1145, 254)  # nome
    time.sleep(1)
    escrever_mensagem("recepção/em mãos (dia 28/07/2025)")
    time.sleep(1)
    clicar(847, 384)   # RG
    time.sleep(1)
    clicar(1122, 469)  # documento
    time.sleep(1)
    escrever_mensagem(gerar_numero_rg())
    time.sleep(1)
    clicar(1080, 605)  # casa
    time.sleep(1)
    clicar(1089, 1044) # descer teclado
    time.sleep(1)
    clicar(799, 773)   # câmera
    time.sleep(1)

    for _ in range(2):
        clicar(963, 996)   # ícone foto
        time.sleep(1)
        clicar(1096, 981)  # confirmar
        time.sleep(1)

    clicar(882, 627)   # cancelar
    time.sleep(1)
    arrastar(1104, 839, 1074, 610)  # descer tela
    time.sleep(1)
    clicar(1046, 874)  # observação
    time.sleep(1)
    escrever_mensagem("Qualquer duvida entre em contato: (21) 99950-9605 shoppe.")
    time.sleep(1)
    clicar(1089, 1044)  # descer teclado
    time.sleep(1)
    clicar(956, 990)    # próximo
    time.sleep(1)
    clicar(1058, 973)   # confirmar
    time.sleep(1)
    clicar(964, 464)    # voltar lista
    time.sleep(1)
    clicar(788, 182)    # pendentes
    time.sleep(1)


py.confirm("Clique em OK para iniciar a automação.", "Iniciar Automação")
# Executa o fluxo 2x
for i in range(2):
    print(f"Executando fluxo {i+1}")
    executar_fluxo()
