import pyautogui as py
import time
import random
import pyperclip

mensagem = (
    "Boa tarde! aqui é da shoppe!\n"
    "Seu(s) pedidos foram retirados para entrega, eles serão entregues dia 28/07/2025 (segunda-feira).\n"
    "Caso receba uma mensagem de confirmação de entrega, *por favor*, aguarde até as 18h do dia *28/07/2025 (segunda-feira)*, para responder.\n"
    "Caso venha a responder antes, é possível que o entregador tenha que voltar a sua residência solicitando a devolução do pacote. Boa Tarde!\n"
    "Se ficou alguma duvida, responda a essa mensagem."
)

nome = (
        "recepção/em mãos (dia 28/07/2025)"
)

observacao = (
    "Qualquer duvida entre em contato: (21) 99950-9605 shoppe."
    )


def escrever_mensagem(msg):
    py.typewrite(msg, interval=0.02)
    time.sleep(1)

def colar_codigo_copiado():
    py.click(1272,1028)
    time.sleep(1)
    py.click(1618,964)
    time.sleep(1)

def clicar(x, y):
    py.click(x, y)
    time.sleep(1)

def arrastar(x1, y1, x2, y2):
    py.moveTo(x1, y1)
    py.mouseDown()
    time.sleep(1)
    py.moveTo(x2, y2, duration=0.5)
    py.mouseUp()
    time.sleep(2)

def gerar_numero_rg():
    return ''.join([str(random.randint(0, 9)) for _ in range(8)])


def executar_fluxo():
    py.click(1052,619)  # telefone
    time.sleep(1)
    py.click(899, 924)   # whatsapp
    time.sleep(5)
    py.click(905, 981)   # caixa de texto
    time.sleep(1)
    escrever_mensagem(mensagem)
    py.click(1154, 688)
    time.sleep(3)

    clicar(825, 1044)  # opções janela
    time.sleep(1)
    clicar(1063,627)   # shoppe
    time.sleep(1)
    clicar(963, 561)   # entrar no pedido
    time.sleep(1)
    clicar(979, 214)   # copiar código
    time.sleep(1)
    clicar(1071, 976)  # entregue
    time.sleep(1)
    clicar(1156, 122)  # editar
    time.sleep(1)
    colar_codigo_copiado()
    time.sleep(2)
    clicar(1119,677)  # confirmar
    time.sleep(1)
    clicar(812, 350)   # outros
    time.sleep(1)
    clicar(1158, 921)  # conexao
    time.sleep(1)
    clicar(969, 975)   # confirmar
    time.sleep(1)
    clicar(1145, 254)  # nome
    time.sleep(3)
    escrever_mensagem(nome)
    time.sleep(3)
    clicar(847, 384)   # RG
    time.sleep(2)
    clicar(1122, 469)  # documento
    time.sleep(3)
    escrever_mensagem(gerar_numero_rg())
    time.sleep(3)
    clicar(1080, 605)  # casa
    time.sleep(1)
    clicar(1089, 1044) # descer teclado
    time.sleep(1)
    clicar(799, 773)   # câmera
    time.sleep(1)

    for _ in range(2):
        clicar(963, 996)   # ícone foto
        time.sleep(1.6)
        clicar(1096, 981)  # confirmar
        time.sleep(1.6)

    clicar(882, 627)   # cancelar
    time.sleep(2)
    arrastar(1104, 839, 1074, 610)  # descer tela
    time.sleep(2)
    clicar(1046, 874)  # observação
    time.sleep(2)
    escrever_mensagem(observacao)
    time.sleep(2)
    clicar(1089, 1044)  # descer teclado
    time.sleep(2)
    clicar(956, 990)    # próximo
    time.sleep(2)
    clicar(1058, 973)   # confirmar
    time.sleep(7)
    clicar(964, 464)    # voltar lista
    time.sleep(2)
    clicar(788, 182)    # pendentes
    time.sleep(2)


py.confirm("Clique em OK para iniciar a automação.", "Iniciar Automação")
# Executa o fluxo 2x

for i in range(107):
    print(f"Executando fluxo {i+1}")
    executar_fluxo()
