import pyautogui as py
import time
import random

def arrastar(x1, y1, x2, y2):
    py.moveTo(x1, y1)
    py.mouseDown()
    time.sleep(1)
    py.moveTo(x2, y2, duration=0.5)
    py.mouseUp()
    time.sleep(2)

def gerar_numero_rg():
    return ''.join([str(random.randint(0, 9)) for _ in range(8)])

def executar_fluxo():
    py.click(1052, 637)  # telefone
    time.sleep(1)
    py.click(899, 924)   # whatsapp
    time.sleep(2.5)
    py.click(905, 981)   # caixa de texto
    time.sleep(1)
    py.write("Boa tarde! aqui é da shoppe!\nSeu(s) pedidos foram retirados para entrega, eles serao entregues dia 28/07/2025 (segunda-feira).\nCaso receba uma mensagem de confirmacao de entrega, *por favor*, aguarde até as 18h do dia *28/07/2025 (segunda-feira)*, para responder.\nCaso venha a responder antes, e possivel que o entregador tenha que voltar a sua residencia solicitando a devolucao do pacote. Boa Tarde!\nSe ficou alguma duvida, responda a essa mensagem.", interval=0.05)
    py.press('enter')
    time.sleep(2)

    py.click(825, 1044)  # opções janela
    time.sleep(1)
    py.click(951, 475)   # shoppe
    time.sleep(1)
    py.click(967, 494)   # copiar código
    time.sleep(1)
    py.click(963, 561)   # entrar no pedido
    time.sleep(1)
    py.click(1071, 976)  # entregue
    time.sleep(1)
    py.click(1156, 122)  # editar
    time.sleep(1)
    py.hotkey('ctrl', 'v')
    time.sleep(2)
    py.click(1145, 594)  # confirmar
    time.sleep(1)
    py.click(812, 350)   # outros
    time.sleep(1)
    py.click(1158, 921)  # conexao
    time.sleep(1)
    py.click(969, 975)   # confirmar
    time.sleep(1)
    py.click(1145, 254)  # nome
    time.sleep(1)
    py.write("recepcao/em maos (dia 28/07/2025)", 0.05)
    time.sleep(1)
    py.click(847, 384)   # RG
    time.sleep(1)
    py.click(1122, 469)  # documento
    time.sleep(1)
    py.write(gerar_numero_rg())
    time.sleep(1)
    py.click(1080, 605)  # casa
    time.sleep(1)
    py.click(1089, 1044) # descer teclado
    time.sleep(1)
    py.click(799, 773)   # câmera
    time.sleep(1)

    for _ in range(2):
        py.click(963, 996)   # ícone foto
        time.sleep(1)
        py.click(1096, 981)  # confirmar
        time.sleep(1)

    py.click(882, 627)   # cancelar
    time.sleep(1)
    arrastar(1104, 839, 1074, 610)  # descer tela
    time.sleep(1)
    py.click(1046, 874)  # observação
    time.sleep(1)
    py.write("Qualquer duvida entre em contato: (21) 99950-9605 shoppe.")
    time.sleep(1)
    py.click(1089, 1044)  # descer teclado
    time.sleep(1)
    py.click(956, 990)    # próximo
    time.sleep(1)
    py.click(1058, 973)   # confirmar
    time.sleep(1)
    py.click(964, 464)    # voltar lista
    time.sleep(1)
    py.click(788, 182)    # pendentes
    time.sleep(1)


py.confirm("Clique em OK para iniciar a automação.", "Iniciar Automação")
# Executa o fluxo 2x
for i in range(2):
    print(f"Executando fluxo {i+1}")
    executar_fluxo()
