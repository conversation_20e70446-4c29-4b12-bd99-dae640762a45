import pywhatkit as pwk
import time
import random
import re
from datetime import datetime, timedelta

# Configurações anti-spam
DELAY_MIN = 10  # Delay mínimo entre mensagens (segundos)
DELAY_MAX = 20  # Delay máximo entre mensagens (segundos)
BATCH_SIZE = 8   # Quantas mensagens enviar antes de uma pausa longa
LONG_PAUSE_MIN = 120  # Pausa longa mínima (segundos)
LONG_PAUSE_MAX = 180  # Pausa longa máxima (segundos)

mensagem = (
    "Boa tarde! aqui é da shoppe!\n"
    "Seu(s) pedidos foram retirados para entrega, eles serão entregues dia 28/07/2025 (segunda-feira).\n"
    "Caso receba uma mensagem de confirmação de entrega, *por favor*, aguarde até as 18h do dia *28/07/2025 (segunda-feira)*, para responder.\n"
    "Caso venha a responder antes, é possível que o entregador tenha que voltar a sua residência solicitando a devolução do pacote. Boa Tarde!\n"
    "Se ficou alguma duvida, responda a essa mensagem."
)

def ler_telefones():
    """Lê os telefones do arquivo e remove duplicatas"""
    telefones = []
    try:
        with open('telefones_extraidos.txt', 'r', encoding='utf-8') as f:
            for linha in f:
                linha = linha.strip()
                if linha and not linha.startswith('CÓDIGOS') and not linha.startswith('='):
                    # Extrair apenas o número (após os dois pontos)
                    if ':' in linha:
                        numero = linha.split(':', 1)[1].strip()
                    else:
                        numero = linha
                    
                    # Limpar e validar número
                    numero_limpo = re.sub(r'[^\d+]', '', numero)
                    
                    # Validar se é um número válido (deve ter pelo menos 10 dígitos)
                    if len(numero_limpo.replace('+', '')) >= 10:
                        telefones.append(numero_limpo)
    
    except FileNotFoundError:
        print("Arquivo telefones_extraidos.txt não encontrado!")
        return []
    
    # Remover duplicatas mantendo a ordem
    telefones_unicos = []
    for tel in telefones:
        if tel not in telefones_unicos and tel != "Número não encontrado":
            telefones_unicos.append(tel)
    
    return telefones_unicos

def enviar_mensagem_pywhatkit(numero, mensagem_texto):
    """Envia mensagem usando a biblioteca pywhatkit"""
    try:
        # Formatar número (garantir que tenha +)
        if not numero.startswith('+'):
            numero = '+' + numero
        
        # Calcular horário para envio (1 minuto no futuro)
        agora = datetime.now()
        envio = agora + timedelta(minutes=1)
        
        # Enviar mensagem instantânea
        pwk.sendwhatmsg_instantly(numero, mensagem_texto, wait_time=5, tab_close=True)
        
        return True
        
    except Exception as e:
        print(f"Erro ao enviar mensagem para {numero}: {e}")
        return False

def enviar_mensagem_agendada(numero, mensagem_texto, hora, minuto):
    """Envia mensagem agendada usando pywhatkit"""
    try:
        # Formatar número (garantir que tenha +)
        if not numero.startswith('+'):
            numero = '+' + numero
        
        # Enviar mensagem agendada
        pwk.sendwhatmsg(numero, mensagem_texto, hora, minuto, wait_time=15, tab_close=True)
        
        return True
        
    except Exception as e:
        print(f"Erro ao enviar mensagem para {numero}: {e}")
        return False

def delay_inteligente(contador_mensagens):
    """Aplica delay inteligente baseado no número de mensagens enviadas"""
    if contador_mensagens % BATCH_SIZE == 0 and contador_mensagens > 0:
        # Pausa longa a cada BATCH_SIZE mensagens
        delay = random.uniform(LONG_PAUSE_MIN, LONG_PAUSE_MAX)
        print(f"Pausa longa de {delay:.1f} segundos após {contador_mensagens} mensagens...")
    else:
        # Pausa normal entre mensagens
        delay = random.uniform(3, 8)
        print(f"Aguardando {delay:.1f} segundos...")
    
    time.sleep(delay)

def main():
    print("=== ENVIO AUTOMÁTICO COM PYWHATKIT ===")
    print("Carregando telefones...")
    
    telefones = ler_telefones()
    
    if not telefones:
        print("Nenhum telefone válido encontrado!")
        return
    
    print(f"Encontrados {len(telefones)} números únicos para envio")
    print(f"Configuração anti-spam:")
    print(f"- Delay entre mensagens: {DELAY_MIN}-{DELAY_MAX}s")
    print(f"- Pausa longa a cada {BATCH_SIZE} mensagens: {LONG_PAUSE_MIN}-{LONG_PAUSE_MAX}s")
    
    print("\nMétodos disponíveis:")
    print("1. Envio instantâneo (recomendado)")
    print("2. Envio agendado")
    
    metodo = input("Escolha o método (1 ou 2): ")
    
    if metodo not in ['1', '2']:
        print("Método inválido!")
        return
    
    # Confirmação do usuário
    input("\nPressione Enter para iniciar o envio (Ctrl+C para cancelar)...")
    
    sucessos = 0
    erros = 0
    
    if metodo == '1':
        # Envio instantâneo
        for i, numero in enumerate(telefones, 1):
            print(f"\n[{i}/{len(telefones)}] Enviando para: {numero}")
            
            sucesso = enviar_mensagem_pywhatkit(numero, mensagem)
            
            if sucesso:
                sucessos += 1
                print(f"✅ Mensagem enviada com sucesso!")
            else:
                erros += 1
                print(f"❌ Erro no envio")
            
            # Aplicar delay inteligente (exceto na última mensagem)
            if i < len(telefones):
                delay_inteligente(i)
    
    else:
        # Envio agendado
        agora = datetime.now()
        
        for i, numero in enumerate(telefones, 1):
            # Calcular horário de envio (espaçado por 2 minutos)
            tempo_envio = agora + timedelta(minutes=i*2)
            hora = tempo_envio.hour
            minuto = tempo_envio.minute
            
            print(f"\n[{i}/{len(telefones)}] Agendando para {numero} às {hora:02d}:{minuto:02d}")
            
            sucesso = enviar_mensagem_agendada(numero, mensagem, hora, minuto)
            
            if sucesso:
                sucessos += 1
                print(f"✅ Mensagem agendada com sucesso!")
            else:
                erros += 1
                print(f"❌ Erro no agendamento")
            
            time.sleep(2)  # Pequeno delay entre agendamentos
    
    print(f"\n=== RELATÓRIO FINAL ===")
    print(f"Total de números: {len(telefones)}")
    print(f"Sucessos: {sucessos}")
    print(f"Erros: {erros}")
    print(f"Taxa de sucesso: {(sucessos/len(telefones)*100):.1f}%")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\nEnvio cancelado pelo usuário!")
    except Exception as e:
        print(f"\nErro inesperado: {e}")
