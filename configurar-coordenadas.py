import pyautogui as py
import time

def descobrir_coordenadas():
    """Ajuda a descobrir as coordenadas corretas do WhatsApp Web"""
    
    print("=== CONFIGURADOR DE COORDENADAS WHATSAPP WEB ===")
    print("\nEste script ajuda a descobrir as coordenadas corretas para:")
    print("1. Caixa de busca do WhatsApp Web")
    print("2. Caixa de texto para mensagens")
    print("\nInstruções:")
    print("- Abra o WhatsApp Web no navegador")
    print("- Posicione o mouse sobre o elemento desejado")
    print("- Pressione Ctrl+C para capturar a posição")
    print("- Pressione Ctrl+X para sair")
    
    coordenadas = {}
    
    # Descobrir coordenada da caixa de busca
    print("\n1. CAIXA DE BUSCA:")
    print("Posicione o mouse sobre a caixa de busca do WhatsApp Web")
    print("Pressione Ctrl+C quando estiver na posição correta...")
    
    while True:
        try:
            if py.hotkey('ctrl', 'c'):
                break
        except:
            pass
        
        try:
            pos = py.position()
            print(f"\rPosição atual: {pos}", end="", flush=True)
            time.sleep(0.1)
        except KeyboardInterrupt:
            pos = py.position()
            coordenadas['busca'] = pos
            print(f"\n✅ Caixa de busca: {pos}")
            break
    
    time.sleep(1)
    
    # Descobrir coordenada da caixa de mensagem
    print("\n2. CAIXA DE MENSAGEM:")
    print("Posicione o mouse sobre a caixa de texto para mensagens")
    print("Pressione Ctrl+C quando estiver na posição correta...")
    
    while True:
        try:
            pos = py.position()
            print(f"\rPosição atual: {pos}", end="", flush=True)
            time.sleep(0.1)
        except KeyboardInterrupt:
            pos = py.position()
            coordenadas['mensagem'] = pos
            print(f"\n✅ Caixa de mensagem: {pos}")
            break
    
    # Mostrar código para copiar
    print("\n" + "="*50)
    print("COORDENADAS DESCOBERTAS:")
    print("="*50)
    
    if 'busca' in coordenadas:
        print(f"Caixa de busca: {coordenadas['busca']}")
        print(f"Código: py.click({coordenadas['busca'][0]}, {coordenadas['busca'][1]})")
    
    if 'mensagem' in coordenadas:
        print(f"Caixa de mensagem: {coordenadas['mensagem']}")
        print(f"Código: py.click({coordenadas['mensagem'][0]}, {coordenadas['mensagem'][1]})")
    
    print("\nCopie estes valores para o arquivo script-whatsapp-send.py")
    print("nas linhas correspondentes da função enviar_mensagem_otimizado()")

def testar_coordenadas():
    """Testa as coordenadas atuais"""
    print("=== TESTE DE COORDENADAS ===")
    print("Este teste irá clicar nas coordenadas configuradas")
    print("Certifique-se de que o WhatsApp Web está aberto")
    
    input("Pressione Enter para testar a caixa de busca...")
    
    # Coordenadas atuais do script
    busca_x, busca_y = 200, 150
    mensagem_x, mensagem_y = 500, 650
    
    print(f"Clicando na caixa de busca ({busca_x}, {busca_y})...")
    py.click(busca_x, busca_y)
    time.sleep(2)
    
    print("Digitando número de teste...")
    py.typewrite("21999887766", interval=0.1)
    time.sleep(2)
    
    input("Pressione Enter para testar a caixa de mensagem...")
    
    print(f"Clicando na caixa de mensagem ({mensagem_x}, {mensagem_y})...")
    py.click(mensagem_x, mensagem_y)
    time.sleep(2)
    
    print("Digitando mensagem de teste...")
    py.typewrite("Teste de coordenadas", interval=0.1)
    
    print("\n✅ Teste concluído!")
    print("Se os cliques não foram nos locais corretos,")
    print("execute a opção 1 para descobrir as coordenadas certas.")

def main():
    print("=== CONFIGURADOR WHATSAPP WEB ===")
    print("1. Descobrir coordenadas")
    print("2. Testar coordenadas atuais")
    print("3. Sair")
    
    opcao = input("\nEscolha uma opção: ")
    
    if opcao == "1":
        descobrir_coordenadas()
    elif opcao == "2":
        testar_coordenadas()
    elif opcao == "3":
        print("Saindo...")
    else:
        print("Opção inválida!")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\nScript interrompido pelo usuário!")
    except Exception as e:
        print(f"\nErro: {e}")
