
import pyautogui as py
import time
import random
import re
from urllib.parse import quote

# Configurações anti-spam
DELAY_MIN = 8  # Delay mínimo entre mensagens (segundos)
DELAY_MAX = 15  # Delay máximo entre mensagens (segundos)
BATCH_SIZE = 10  # Quantas mensagens enviar antes de uma pausa longa
LONG_PAUSE_MIN = 60  # Pausa longa mínima (segundos)
LONG_PAUSE_MAX = 120  # Pausa longa máxima (segundos)

mensagem = (
    "Boa tarde! aqui é da shoppe!\n"
    "Seu(s) pedidos foram retirados para entrega, eles serão entregues dia 28/07/2025 (segunda-feira).\n"
    "Caso receba uma mensagem de confirmação de entrega, *por favor*, aguarde até as 18h do dia *28/07/2025 (segunda-feira)*, para responder.\n"
    "Caso venha a responder antes, é possível que o entregador tenha que voltar a sua residência solicitando a devolução do pacote. Boa Tarde!\n"
    "Se ficou alguma duvida, responda a essa mensagem."
)

def ler_telefones():
    """Lê os telefones do arquivo e remove duplicatas"""
    telefones = []
    try:
        with open('telefones_extraidos.txt', 'r', encoding='utf-8') as f:
            for linha in f:
                linha = linha.strip()
                if linha and not linha.startswith('CÓDIGOS') and not linha.startswith('='):
                    # Extrair apenas o número (após os dois pontos)
                    if ':' in linha:
                        numero = linha.split(':', 1)[1].strip()
                    else:
                        numero = linha

                    # Limpar e validar número
                    numero_limpo = re.sub(r'[^\d+]', '', numero)

                    # Validar se é um número válido (deve ter pelo menos 10 dígitos)
                    if len(numero_limpo.replace('+', '')) >= 10:
                        telefones.append(numero_limpo)

    except FileNotFoundError:
        print("Arquivo telefones_extraidos.txt não encontrado!")
        return []

    # Remover duplicatas mantendo a ordem
    telefones_unicos = []
    for tel in telefones:
        if tel not in telefones_unicos and tel != "Número não encontrado":
            telefones_unicos.append(tel)

    return telefones_unicos

def enviar_mensagem_url(numero, mensagem_texto):
    """Método usando URL do WhatsApp Web (mais rápido)"""
    try:
        # Formatar número (remover + se existir)
        numero_formatado = numero.replace('+', '')

        # Codificar mensagem para URL
        mensagem_codificada = quote(mensagem_texto)

        # Criar URL do WhatsApp Web
        url = f"https://web.whatsapp.com/send?phone={numero_formatado}&text={mensagem_codificada}"

        # Abrir nova aba
        py.hotkey('ctrl', 't')
        time.sleep(1)

        # Navegar para URL
        py.typewrite(url, interval=0.01)
        py.press('enter')

        # Aguardar carregamento
        time.sleep(6)

        # Enviar mensagem (Enter)
        py.press('enter')
        time.sleep(2)

        # Fechar aba
        py.hotkey('ctrl', 'w')
        time.sleep(1)

        return True

    except Exception as e:
        print(f"Erro ao enviar mensagem para {numero}: {e}")
        return False

def delay_inteligente(contador_mensagens):
    """Aplica delay inteligente baseado no número de mensagens enviadas"""
    if contador_mensagens % BATCH_SIZE == 0 and contador_mensagens > 0:
        # Pausa longa a cada BATCH_SIZE mensagens
        delay = random.uniform(LONG_PAUSE_MIN, LONG_PAUSE_MAX)
        print(f"Pausa longa de {delay:.1f} segundos após {contador_mensagens} mensagens...")
    else:
        # Pausa normal entre mensagens
        delay = random.uniform(DELAY_MIN, DELAY_MAX)
        print(f"Aguardando {delay:.1f} segundos...")

    time.sleep(delay)

def main():
    print("=== ENVIO AUTOMÁTICO DE MENSAGENS WHATSAPP ===")
    print("Carregando telefones...")

    telefones = ler_telefones()

    if not telefones:
        print("Nenhum telefone válido encontrado!")
        return

    print(f"Encontrados {len(telefones)} números únicos para envio")
    print(f"Configuração anti-spam:")
    print(f"- Delay entre mensagens: {DELAY_MIN}-{DELAY_MAX}s")
    print(f"- Pausa longa a cada {BATCH_SIZE} mensagens: {LONG_PAUSE_MIN}-{LONG_PAUSE_MAX}s")

    # Confirmação do usuário
    input("\nPressione Enter para iniciar o envio (Ctrl+C para cancelar)...")

    # Aguardar usuário focar no WhatsApp Web
    print("Focando no WhatsApp Web em 5 segundos...")
    time.sleep(5)

    sucessos = 0
    erros = 0

    for i, numero in enumerate(telefones, 1):
        print(f"\n[{i}/{len(telefones)}] Enviando para: {numero}")

        # Tentar envio via URL (mais rápido e confiável)
        sucesso = enviar_mensagem_url(numero, mensagem)

        if sucesso:
            sucessos += 1
            print(f"✅ Mensagem enviada com sucesso!")
        else:
            erros += 1
            print(f"❌ Erro no envio - tentando método alternativo...")

        # Aplicar delay inteligente (exceto na última mensagem)
        if i < len(telefones):
            delay_inteligente(i)

    print(f"\n=== RELATÓRIO FINAL ===")
    print(f"Total de números: {len(telefones)}")
    print(f"Sucessos: {sucessos}")
    print(f"Erros: {erros}")
    print(f"Taxa de sucesso: {(sucessos/len(telefones)*100):.1f}%")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\nEnvio cancelado pelo usuário!")
    except Exception as e:
        print(f"\nErro inesperado: {e}")